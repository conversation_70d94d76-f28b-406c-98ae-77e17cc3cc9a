import 'dart:async';
import 'dart:developer';
import 'package:animated_hint_textfield/animated_hint_textfield.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as r;
import 'package:hive/hive.dart';
import 'package:provider/provider.dart';
import 'package:schnell_pole_installation/Provider/provider.dart';
import 'package:schnell_pole_installation/survey/excel_export.dart';
import 'package:schnell_pole_installation/survey/models/base_survey_model.dart';
import 'package:schnell_pole_installation/survey/models/pole_survey_model.dart';
import 'package:schnell_pole_installation/survey/models/sp_survey_model.dart';
import 'package:schnell_pole_installation/survey/models/transformer_model.dart';
import 'package:schnell_pole_installation/survey/survey_controller.dart';
import 'package:schnell_pole_installation/utils/box_container.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'package:schnell_pole_installation/utils/dialog_box.dart';
import 'package:schnell_pole_installation/utils/loader.dart';
import 'package:schnell_pole_installation/utils/utility.dart';
import '../models/survey_image_model.dart';
import 'survey_take_photo.dart';

class SwitchPointSurvey extends r.ConsumerStatefulWidget {
  const SwitchPointSurvey({super.key});
  @override
  r.ConsumerState<SwitchPointSurvey> createState() => _SwitchPointSurveyState();
}

class _SwitchPointSurveyState extends r.ConsumerState<SwitchPointSurvey>
    with WidgetsBindingObserver {
  // late List<ConnectivityResult> connectivityResult;
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  List<ConnectivityResult> _connectionStatus = [ConnectivityResult.none];
  final Connectivity _connectivity = Connectivity();
  bool hasInternet = false;
  late Timer sendDatatimer;
  late Timer sendImagetimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(surveyController).fetchLocationForSurvey();
      // Check initial connectivity status
      _checkInitialConnectivity();
    });
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    sendDatatimer = Timer.periodic(const Duration(minutes: 10), (value) {
      uploadSurveyData(context);
    });
    sendImagetimer = Timer.periodic(const Duration(minutes: 10), (value) {
      uploadCapturedImages(context);
    });
  }

  /// Check initial connectivity status when screen loads
  Future<void> _checkInitialConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(result);
    } catch (e) {
      log('Error checking initial connectivity: $e');
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // App came back to foreground, check connectivity
      log('App resumed - checking connectivity status');
      _checkConnectivityOnResume();
    }
  }

  /// Check connectivity when app resumes or screen comes back into focus
  Future<void> _checkConnectivityOnResume() async {
    try {
      final result = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(result);
    } catch (e) {
      log('Error checking connectivity on resume: $e');
    }
  }

  Future<void> initConnectivity() async {
    late List<ConnectivityResult> result;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      result = await _connectivity.checkConnectivity();
      debugPrint('Connection Status : $result');
    } on PlatformException catch (e) {
      debugPrint(
        'Couldn\'t check connectivity status : $e',
      );
      return;
    }
    if (!mounted) {
      return Future.value(null);
    }
    return _updateConnectionStatus(result);
  }

  // Debounce mechanism for connectivity changes
  static DateTime? _lastConnectivityChange;
  static const Duration _connectivityDebounceDelay = Duration(seconds: 3);

  Future<void> _updateConnectionStatus(List<ConnectivityResult> result) async {
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    setState(() {
      _connectionStatus = result;
    });
    debugPrint('Update Install Connection Status : $_connectionStatus');

    if (_connectionStatus.contains(ConnectivityResult.none) ||
        _connectionStatus.isEmpty) {
      setState(() {
        hasInternet = false;
      });
      dataBloc.hasInternet = false;
      log('Internet connection lost');
    } else {
      // Debounce connectivity changes to prevent rapid upload attempts
      final now = DateTime.now();
      if (_lastConnectivityChange != null &&
          now.difference(_lastConnectivityChange!) <
              _connectivityDebounceDelay) {
        log('Connectivity change debounced - too soon since last change');
        setState(() {
          hasInternet = true;
        });
        dataBloc.hasInternet = true;
        return;
      }
      _lastConnectivityChange = now;

      log('Internet connection restored - triggering upload');

      // Trigger uploads when internet is restored
      uploadSurveyData(context);
      uploadCapturedImages(context);

      setState(() {
        hasInternet = true;
      });
      dataBloc.hasInternet = true;
    }
  }

  // Track which records are currently being uploaded to prevent duplicates
  static final Set<String> _uploadingRecords = <String>{};

  // Debounce mechanism to prevent rapid successive calls
  static DateTime? _lastUploadAttempt;
  static const Duration _uploadDebounceDelay = Duration(seconds: 5);

  uploadSurveyData(BuildContext context) async {
    // Debounce: prevent rapid successive calls
    final now = DateTime.now();
    if (_lastUploadAttempt != null &&
        now.difference(_lastUploadAttempt!) < _uploadDebounceDelay) {
      log('Upload attempt debounced - too soon since last attempt');
      return;
    }
    _lastUploadAttempt = now;
    try {
      bool isOnline = await Utility.isConnected();
      if (!isOnline) {
        log('No internet connection available for survey upload');
        return;
      }

      final box = await Hive.openBox('surveyBox');
      log('surveyBox: $box');
      int totalRecords = box.keys.length;
      int uploadedCount = 0;
      int alreadyUploadedCount = 0;
      int skippedCount = 0;

      log('Starting survey upload process. Total records: $totalRecords');

      for (final key in box.keys) {
        try {
          final keyString = key.toString();
          final map = box.get(key) as Map<dynamic, dynamic>;
          log('map : $map');
          final BaseSurveyModel model;
          if (map['assetType'] == 'Pole') {
            model = PoleSurveyModel.fromMap(map);
          } else if (map['assetType'] == 'Switch Point') {
            model = SpSurveyModel.fromMap(map);
          } else if (map['assetType'] == 'Transformer') {
            model = TransformerSurveyModel.fromMap(map);
          } else {
            log('Unknown assetType: ${map['assetType']}');
            continue;
          }
          log('map : $model');
          // Skip if already uploaded
          if (model.isUploaded) {
            alreadyUploadedCount++;
            if (model is PoleSurveyModel) {
              log('Survey for pole ${model.poleNumber} already uploaded, skipping');
            } else if (model is SpSurveyModel) {
              log('Survey for switch point ${model.spNo} already uploaded, skipping');
            } else if (model is TransformerSurveyModel) {
              log('Survey for Transformer ${model.transformerNo} already uploaded, skipping');
            }
            continue;
          }

          // Skip if currently being uploaded by another process
          if (_uploadingRecords.contains(keyString)) {
            skippedCount++;
            // log('Survey for pole ${model.poleNumber} is currently being uploaded, skipping');
            continue;
          }

          // Mark as being uploaded
          _uploadingRecords.add(keyString);

          try {
            if (model is PoleSurveyModel) {
              log('Uploading survey for pole: ${model.poleNumber}');
              bool success = await ref
                  .read(surveyController)
                  .updateSurveyDetails(context, model.toMap());
              if (success) {
                // Re-read the record to ensure we have the latest data
                final latestMap = box.get(key) as Map<dynamic, dynamic>;
                log('latest map : $latestMap');
                final latestModel = PoleSurveyModel.fromMap(latestMap);

                // Only update if it's still not uploaded (avoid race conditions)
                if (!latestModel.isUploaded) {
                  latestModel.isUploaded = true;

                  await box.put(key, (latestModel).toMap());

                  uploadedCount++;

                  log('Successfully uploaded and marked survey for pole: ${model.poleNumber}');
                } else {
                  // log('Survey for pole ${model.poleNumber} was already marked as uploaded by another process');
                  alreadyUploadedCount++;
                }
              } else {
                log('Failed to upload survey for pole: ${model.poleNumber}');
              }
            } else if (model is SpSurveyModel) {
              log('Uploading survey for pole: ${model.spNo}');
              bool success = await ref
                  .read(surveyController)
                  .updateSurveyDetails(context, model.toMap());
              if (success) {
                // Re-read the record to ensure we have the latest data
                final latestMap = box.get(key) as Map<dynamic, dynamic>;
                log('latest map : $latestMap');
                final latestModel = SpSurveyModel.fromMap(latestMap);

                // Only update if it's still not uploaded (avoid race conditions)
                if (!latestModel.isUploaded) {
                  latestModel.isUploaded = true;

                  await box.put(key, (latestModel).toMap());

                  uploadedCount++;

                  log('Successfully uploaded and marked survey for pole: ${model.spNo}');
                } else {
                  // log('Survey for pole ${model.poleNumber} was already marked as uploaded by another process');
                  alreadyUploadedCount++;
                }
              } else {
                log('Failed to upload survey for pole: ${model.spNo}');
              }
            } else if (model is TransformerSurveyModel) {
              log('Uploading survey for pole: ${model.transformerNo}');
              bool success = await ref
                  .read(surveyController)
                  .updateSurveyDetails(context, model.toMap());
              if (success) {
                // Re-read the record to ensure we have the latest data
                final latestMap = box.get(key) as Map<dynamic, dynamic>;
                log('latest map : $latestMap');
                final latestModel = TransformerSurveyModel.fromMap(latestMap);

                // Only update if it's still not uploaded (avoid race conditions)
                if (!latestModel.isUploaded) {
                  latestModel.isUploaded = true;

                  await box.put(key, (latestModel).toMap());

                  uploadedCount++;

                  log('Successfully uploaded and marked survey for pole: ${model.transformerNo}');
                } else {
                  // log('Survey for pole ${model.poleNumber} was already marked as uploaded by another process');
                  alreadyUploadedCount++;
                }
              } else {
                log('Failed to upload survey for pole: ${model.transformerNo}');
              }
            }
          } finally {
            // Always remove from uploading set
            _uploadingRecords.remove(keyString);
          }
        } catch (e) {
          log('Error processing survey record with key $key: $e');
        }
      }

      log('Survey upload completed. Uploaded: $uploadedCount, Already uploaded: $alreadyUploadedCount, Skipped (in progress): $skippedCount, Total: $totalRecords');
    } catch (e) {
      log('Error in uploadSurveyData: $e');
    }
  }

  // Track which image records are currently being uploaded
  static final Set<String> _uploadingImages = <String>{};

  // Debounce mechanism for image uploads
  static DateTime? _lastImageUploadAttempt;

  uploadCapturedImages(BuildContext context) async {
    // Debounce: prevent rapid successive calls
    final now = DateTime.now();
    if (_lastImageUploadAttempt != null &&
        now.difference(_lastImageUploadAttempt!) < _uploadDebounceDelay) {
      log('Image upload attempt debounced - too soon since last attempt');
      return;
    }
    _lastImageUploadAttempt = now;
    try {
      final isConnected = await Utility.isConnected();
      if (!isConnected) {
        log('No internet connection available for image upload');
        return;
      }

      final imagesBox = await Hive.openBox('imagesBox');
      log('Starting image upload process. Total image records: ${imagesBox.length}');

      for (int i = 0; i < imagesBox.length; i++) {
        try {
          final imageKey = 'image_$i';

          // Skip if currently being uploaded
          if (_uploadingImages.contains(imageKey)) {
            log('Image record $i is currently being uploaded, skipping');
            continue;
          }

          final imageMap = imagesBox.getAt(i) as Map<dynamic, dynamic>;
          final model = MultiCapturedImageModel.fromMap(imageMap);

          // Skip if all images are already uploaded
          if (model.isAllUploaded) {
            log('All images for record $i already uploaded, skipping');
            continue;
          }

          // Mark as being uploaded
          _uploadingImages.add(imageKey);

          try {
            bool updated = false;

            // Upload image 1
            if (!model.isUploaded1) {
              if (model.fileName1.isNotEmpty) {
                log('Uploading 1st image: ${model.fileName1}');
                final res1 = await ref.read(surveyController).updateSurveyImage(
                    model.base64Image1, model.fileName1, context);
                log('res1: $res1');
                if (res1 == true) {
                  model.isUploaded1 = true;
                  updated = true;
                  log('✅ Image 1 uploaded: ${model.fileName1}');
                }
              } else {
                // Mark as uploaded if no image was captured
                model.isUploaded1 = true;
                updated = true;
                log('📷 Image 1 not captured - marking as uploaded');
              }
            }

            // Upload image 2
            if (!model.isUploaded2) {
              if (model.fileName2.isNotEmpty) {
                log('Uploading 2nd image: ${model.fileName2}');
                final res2 = await ref.read(surveyController).updateSurveyImage(
                    model.base64Image2, model.fileName2, context);
                log('res2: $res2');
                if (res2 == true) {
                  model.isUploaded2 = true;
                  updated = true;
                  log('✅ Image 2 uploaded: ${model.fileName2}');
                }
              } else {
                // Mark as uploaded if no image was captured
                model.isUploaded2 = true;
                updated = true;
                log('📷 Image 2 not captured - marking as uploaded');
              }
            }

            // Upload image 3
            if (!model.isUploaded3) {
              if (model.fileName3.isNotEmpty) {
                log('Uploading 3rd image: ${model.fileName3}');
                final res3 = await ref.read(surveyController).updateSurveyImage(
                    model.base64Image3, model.fileName3, context);
                log('res3: $res3');
                if (res3 == true) {
                  model.isUploaded3 = true;
                  updated = true;
                  log('✅ Image 3 uploaded: ${model.fileName3}');
                }
              } else {
                // Mark as uploaded if no image was captured
                model.isUploaded3 = true;
                updated = true;
                log('📷 Image 3 not captured - marking as uploaded');
              }
            }

            // Update the record if any changes were made
            if (updated) {
              await imagesBox.putAt(i, model.toMap());
              log('Updated image record $i with new upload status');
            }

            // Log final status
            final capturedCount = [
              model.fileName1,
              model.fileName2,
              model.fileName3
            ].where((filename) => filename.isNotEmpty).length;
            final uploadedCount = [
              model.isUploaded1,
              model.isUploaded2,
              model.isUploaded3
            ].where((uploaded) => uploaded).length;

            if (model.isAllUploaded) {
              log("✅ All images at index $i processed. Captured: $capturedCount, Uploaded: $uploadedCount");
            } else {
              log("⏳ Images at index $i partially processed. Captured: $capturedCount, Uploaded: $uploadedCount. Will retry later.");
            }
          } finally {
            // Always remove from uploading set
            _uploadingImages.remove(imageKey);
          }
        } catch (e) {
          log('Error processing image record $i: $e');
        }
      }

      log('Image upload process completed');
    } catch (e) {
      log('Error in uploadCapturedImages: $e');
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _connectivitySubscription.cancel();
    sendDatatimer.cancel();
    sendImagetimer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double deviceWidth = MediaQuery.of(context).size.width;
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    final TextEditingController switchPointNumberController =
        ref.watch(surveyController).switchPointNumberController;
    final TextEditingController spMeterNoController =
        ref.watch(surveyController).spMeterNoController;
    final TextEditingController spMakeController =
        ref.watch(surveyController).spMakeController;
    final TextEditingController rrNumberController =
        ref.watch(surveyController).rrNumberController;
    final TextEditingController spTransformerNoController =
        ref.watch(surveyController).spTransformerNoController;
    final TextEditingController switchPointIdController =
        ref.watch(surveyController).switchPointIdController;
    final TextEditingController surveyManualLocationController =
        ref.watch(surveyController).surveyManualLocationEntry;
    final TextEditingController spConnectedLoadController =
        ref.watch(surveyController).spConnectedLoadController;
    final TextEditingController commentsController =
        ref.watch(surveyController).commentsController;
    final selectedSwitchPointType =
        ref.watch(surveyController).selectedSwitchPointType;
    final selectedSPMeter = ref.watch(surveyController).selectedSPMeter;
    final selectedSpEarthingCondition =
        ref.watch(surveyController).selectedSpEarthingCondition;
    final selectedSpCondition = ref.watch(surveyController).selectedSpCondition;
    final selectedSpMeterType = ref.watch(surveyController).selectedSpMeterType;
    final selectedSpPhase = ref.watch(surveyController).selectedSpPhase;
    bool isSurveyLocationFetched =
        ref.watch(surveyController).isSurveyLocationFetched;
    String fetchedLandMark = ref.watch(surveyController).fetchedLandMark;
    String fetchedLat = ref.watch(surveyController).fetchedLat;
    String fetchedLong = ref.watch(surveyController).fetchedLong;
    String fetchedAltitude = ref.watch(surveyController).fetchedAltitude;
    String spNoPrefixText = 'SP';
    String transformerNoPrefixText = 'TFR';

    return Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: lightBlue,
          elevation: 0.0,
          centerTitle: true,
          titleTextStyle: TextStyle(
            color: bDarkBlue,
            fontSize: 18.0,
          ),
          title: Text(
            'Switch Point Asset Survey',
            style: TextStyle(color: bDarkBlue),
          ),
          actions: [
            // Export to Excel button
            IconButton(
              onPressed: () async {
                await showExportDialog(context);
              },
              icon: const Icon(
                Icons.file_download,
                color: Colors.green,
              ),
            ),
            IconButton(
              onPressed: () async {},
              icon: Icon(
                hasInternet ? Icons.cloud : Icons.cloud_off,
                color: hasInternet ? Colors.green : Colors.red,
              ),
            )
          ],
        ),
        body: WillPopScope(
            onWillPop: () async {
              ref.read(surveyController).clearAllUpdatedSelectedFields();
              // Navigator.of(context).pop(true);
              return true;
            },
            child: SafeArea(
                child: SingleChildScrollView(
                    child: Column(mainAxisSize: MainAxisSize.min, children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 10.0, vertical: 10.0),
                child: GestureDetector(
                  child: BoxContainer.rectangleContainer(
                      '${dataBloc.region} > ${dataBloc.zone} > ${dataBloc.ward}'),
                  onTap: () {},
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6.0),
                child: Row(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 0.0),
                        child: TextField(
                          controller: switchPointNumberController,
                          obscureText: false,
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 14,
                            overflow: TextOverflow.ellipsis,
                          ),
                          onChanged: (newValue) {
                            log('switch point number $newValue');
                            ref.read(surveyController).updateSurveyFieldValues(
                                context,
                                ref,
                                newValue.toUpperCase(),
                                'switchPointNumber');
                          },
                          keyboardType: TextInputType.number,
                          maxLength: 6,
                          decoration: InputDecoration(
                            prefixText: spNoPrefixText,
                            prefixStyle: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            counterText: '',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            labelText: 'Switch Point Number *',
                            labelStyle: TextStyle(
                                fontSize: 17,
                                color: darkBlue,
                                fontWeight: FontWeight.bold),
                            floatingLabelBehavior: FloatingLabelBehavior.always,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  Expanded(
                      child: BoxContainer.buildDropdown(
                    context,
                    value: selectedSwitchPointType,
                    label: "Switch Point Type",
                    items: switchPointTypeOptions,
                    onChanged: (value) async {
                      ref.read(surveyController).updateSurveyFieldValues(
                          context, ref, value, 'spType');
                    },
                  )),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10.0, vertical: 6.0),
                      child: TextField(
                        controller: rrNumberController,
                        obscureText: false,
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 14,
                          overflow: TextOverflow.ellipsis,
                        ),
                        onChanged: (newValue) {
                          log('rrNumber $newValue');
                          ref.read(surveyController).updateSurveyFieldValues(
                              context, ref, newValue, 'rrNumber');
                        },
                        maxLength: 25,
                        // keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          counterText: '',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          labelText: 'RR Number',
                          labelStyle: TextStyle(
                              fontSize: 17,
                              color: darkBlue,
                              fontWeight: FontWeight.bold),
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6.0),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        // readOnly: false,
                        controller: spTransformerNoController,
                        obscureText: false,
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 14,
                          overflow: TextOverflow.ellipsis,
                        ),
                        onChanged: (newValue) {
                          log('poleTransFormerNo $newValue');
                          ref.read(surveyController).updateSurveyFieldValues(
                              context,
                              ref,
                              newValue.toUpperCase(),
                              'spTransFormerNo');
                        },
                        maxLength: 15,

                        decoration: InputDecoration(
                          prefixText: transformerNoPrefixText,
                          prefixStyle: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          counterText: '',
                          labelText: 'Transformer No',
                          labelStyle: TextStyle(
                              fontSize: 17,
                              color: darkBlue,
                              fontWeight: FontWeight.bold),
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 15,
                    ),
                    Expanded(
                      child: TextField(
                        controller: switchPointIdController,
                        obscureText: false,
                        maxLength: 13,
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 14,
                          overflow: TextOverflow.ellipsis,
                        ),
                        onChanged: (newValue) {
                          log('SP Id $newValue');
                          ref.read(surveyController).updateSurveyFieldValues(
                              context, ref, newValue, 'spId');
                        },
                        decoration: InputDecoration(
                          counterText: '',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          labelText: 'Panel ID',
                          labelStyle: TextStyle(
                              fontSize: 17,
                              color: darkBlue,
                              fontWeight: FontWeight.bold),
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10.0, vertical: 8.0),
                child: SizedBox(
                  width: deviceWidth,
                  child: InputDecorator(
                      decoration: InputDecoration(
                        label: RichText(
                            text: TextSpan(
                          text: 'Location',
                          style: TextStyle(
                              fontSize: 17,
                              color: darkBlue,
                              fontWeight: FontWeight.bold),
                        )),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10.0),
                        ),
                      ),
                      child: Column(
                        children: [
                          AnimatedTextField(
                            animationType: Animationtype.typer,
                            keyboardType: TextInputType
                                .visiblePassword, // to remove emoji from the keyboard
                            maxLength: 100,

                            controller: surveyManualLocationController,
                            hintTexts: const [
                              'Provide additional landmark information if any',
                            ],
                            style: const TextStyle(color: Colors.black),
                            hintTextStyle: const TextStyle(
                              fontSize: 13,
                            ),
                            onChanged: (value) {
                              ref
                                  .read(surveyController)
                                  .updateSurveyFieldValues(context, ref, value,
                                      'manualEnteredLocation');
                            },

                            decoration: InputDecoration(
                                counterText: '',
                                prefixIcon:
                                    const Icon(Icons.location_on_outlined),
                                contentPadding: const EdgeInsets.all(16.0),
                                filled: true,
                                fillColor:
                                    const Color.fromARGB(248, 64, 124, 161)
                                        .withOpacity(0.20),
                                focusedBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                      color: Color.fromARGB(248, 64, 124, 161),
                                      width: 1.0),
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                      color: Color.fromARGB(248, 64, 124, 161),
                                      width: 1.0),
                                  borderRadius: BorderRadius.circular(8.0),
                                )),
                          ),
                          const SizedBox(height: 20),
                          isSurveyLocationFetched == true
                              ? Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    fetchedLandMark != ''
                                        ? Text(
                                            fetchedLandMark,
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                                fontSize: 13,
                                                color: bDarkBlue,
                                                fontWeight: FontWeight.bold),
                                          )
                                        : Container(),
                                    const SizedBox(
                                      height: 2,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceEvenly,
                                      children: [
                                        const Text(
                                          'Latitude : ',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                        Text(
                                          fetchedLat,
                                          textAlign: TextAlign.center,
                                          style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold),
                                        ),
                                        const Text(
                                          'Longitude : ',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                        Text(
                                          fetchedLong,
                                          textAlign: TextAlign.center,
                                          style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 2,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const Text(
                                          'Altitude : ',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                        Text(
                                          fetchedAltitude,
                                          textAlign: TextAlign.center,
                                          style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ],
                                    ),
                                  ],
                                )
                              : Center(
                                  child: Text(
                                  'Fetching Location...',
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: bDarkBlue),
                                ))
                        ],
                      )),
                ),
              ),
              Row(
                children: [
                  Expanded(
                    child: BoxContainer.buildDropdown(
                      context,
                      value: selectedSpMeterType,
                      label: "Meter Type",
                      items: spMeterTypeOptions,
                      onChanged: (newValue) {
                        log('selected value $newValue');

                        ref.read(surveyController).updateSurveyFieldValues(
                            context, ref, newValue, 'spMeterType');
                      },
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10.0, vertical: 6.0),
                      child: TextField(
                        controller: spConnectedLoadController,
                        obscureText: false,
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 14,
                          overflow: TextOverflow.ellipsis,
                        ),
                        onChanged: (newValue) {
                          log('SP Connected Load $newValue');
                          ref.read(surveyController).updateSurveyFieldValues(
                              context, ref, newValue, 'spConnectedLoad');
                        },
                        keyboardType: const TextInputType.numberWithOptions(
                            decimal: true),
                        decoration: InputDecoration(
                          suffixText: 'kW',
                          suffixStyle: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          labelText: 'Connected Load',
                          labelStyle: TextStyle(
                              fontSize: 17,
                              color: darkBlue,
                              fontWeight: FontWeight.bold),
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              if (selectedSpMeterType != 'DP/Manual')
                Card(
                  color: Theme.of(context).primaryColor,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)),
                  margin: const EdgeInsets.all(10),
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          "Meter Details",
                          style: TextStyle(
                              fontSize: 15, fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(height: 10),
                        Row(
                          children: [
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 10.0, vertical: 6.0),
                                child: TextField(
                                  controller: spMeterNoController,
                                  obscureText: false,
                                  maxLength: 12,
                                  style: const TextStyle(
                                    color: Colors.black,
                                    fontSize: 14,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  onChanged: (newValue) {
                                    log('SP Meter no $newValue');
                                    ref
                                        .read(surveyController)
                                        .updateSurveyFieldValues(context, ref,
                                            newValue, 'spMeterNo');
                                  },
                                  decoration: InputDecoration(
                                    counterText: '',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    labelText: 'S No',
                                    labelStyle: TextStyle(
                                        fontSize: 17,
                                        color: darkBlue,
                                        fontWeight: FontWeight.bold),
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                              child: BoxContainer.buildDropdown(
                                context,
                                value: selectedSPMeter,
                                label: "Meter Status",
                                items: sPMeterOptions,
                                onChanged: (newValue) {
                                  log('selected value $newValue');

                                  ref
                                      .read(surveyController)
                                      .updateSurveyFieldValues(
                                          context, ref, newValue, 'spMeter');
                                },
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 10.0, vertical: 6.0),
                                child: TextField(
                                  controller: spMakeController,
                                  obscureText: false,
                                  style: const TextStyle(
                                    color: Colors.black,
                                    fontSize: 14,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  onChanged: (newValue) {
                                    log('selected value $newValue');

                                    ref
                                        .read(surveyController)
                                        .updateSurveyFieldValues(
                                            context, ref, newValue, 'spMake');
                                  },
                                  decoration: InputDecoration(
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    labelText: 'Make',
                                    labelStyle: TextStyle(
                                        fontSize: 17,
                                        color: darkBlue,
                                        fontWeight: FontWeight.bold),
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                                child: BoxContainer.buildDropdown(
                              context,
                              value: selectedSpPhase,
                              label: "Phase",
                              items: spPhaseOptions,
                              onChanged: (newValue) {
                                log('selected value $newValue');

                                ref
                                    .read(surveyController)
                                    .updateSurveyFieldValues(
                                        context, ref, newValue, 'spPhase');
                              },
                            )),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              Row(
                children: [
                  // Expanded(
                  //   child: Padding(
                  //     padding: const EdgeInsets.symmetric(
                  //         horizontal: 10.0, vertical: 8.0),
                  //     child: Column(
                  //       crossAxisAlignment: CrossAxisAlignment.start,
                  //       children: [
                  //         const Text(
                  //           'Working',
                  //           style: const TextStyle(
                  //               fontSize: 13, fontWeight: FontWeight.w500),
                  //         ),
                  //         const SizedBox(height: 6),
                  //         ToggleSwitch(
                  //           minWidth: 80,
                  //           cornerRadius: 12,
                  //           activeBgColors: [
                  //             [
                  //               const Color.fromARGB(248, 64, 124, 161)
                  //                   .withOpacity(0.8)
                  //             ],
                  //             [
                  //               const Color.fromARGB(248, 64, 124, 161)
                  //                   .withOpacity(0.8)
                  //             ]
                  //           ],
                  //           activeFgColor: Colors.white,
                  //           inactiveBgColor: Colors.grey[300],
                  //           inactiveFgColor: Colors.black,
                  //           totalSwitches: 2,
                  //           labels: const ['Yes', 'No'],
                  //           initialLabelIndex:
                  //               selectedSpConditionWorking ? 0 : 1,
                  //           onToggle: (index) {
                  //             final isYes = index == 0;
                  //             ref
                  //                 .read(surveyController)
                  //                 .updateSurveyFieldValues(
                  //                     context, ref, isYes, 'spMeterWorking');
                  //           },
                  //         ),
                  //       ],
                  //     ),
                  //   ),
                  // ),

                  Expanded(
                    child: BoxContainer.buildDropdown(
                      context,
                      value: selectedSpCondition,
                      label: "Switch Point Condition",
                      items: spConditionOptions,
                      onChanged: (newValue) {
                        log('selected value $newValue');

                        ref.read(surveyController).updateSurveyFieldValues(
                            context, ref, newValue, 'spCondition');
                      },
                    ),
                  ),
                  Expanded(
                      child: BoxContainer.buildDropdown(
                    context,
                    value: selectedSpEarthingCondition,
                    label: "Earthing Condition",
                    items: spEarthingConditionOptions,
                    onChanged: (newValue) {
                      log('selected value $newValue');

                      ref.read(surveyController).updateSurveyFieldValues(
                          context, ref, newValue, 'spEarthingCondition');
                    },
                  )),
                ],
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.12,
                  padding: const EdgeInsets.all(8.0),
                  margin: const EdgeInsets.only(left: 3, right: 3),
                  decoration: BoxDecoration(
                    border: Border.all(color: bDarkBlue),
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Stack(
                    children: [
                      const Positioned.fill(
                        child: Align(
                            alignment: Alignment.topLeft,
                            child: Text(
                              'Remarks / Comments ',
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                              ),
                            )),
                      ),
                      TextField(
                        maxLength: 250,
                        controller: commentsController,
                        style:
                            const TextStyle(color: Colors.black, fontSize: 12),
                        onChanged: (value) {
                          ref.read(surveyController).updateSurveyFieldValues(
                              context, ref, value, "comments");
                        },
                        decoration: const InputDecoration(
                          counterText: '',
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(
                height: 29,
              ),
              GestureDetector(
                  child: Container(
                    height: 50,
                    width: deviceWidth * 0.8,
                    decoration: BoxDecoration(
                        color: const Color.fromARGB(248, 64, 124, 161)
                            .withOpacity(0.8),
                        borderRadius: BorderRadius.circular(10)),
                    child: const Center(
                      child: Text(
                        'Proceed to Photo',
                        style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16),
                      ),
                    ),
                  ),
                  onTap: () async {
                    ref.read(surveyController).onClickEventIsFor('spSurvey');
                    // Check if location is available
                    if (fetchedLat == '' || fetchedLong == '') {
                      alertPopUp(context, 'Collecting Data Please Wait',
                          'assets/animation/userAlert.json');
                      return;
                    }
                    if (switchPointNumberController.text.isEmpty) {
                      FocusScope.of(context).unfocus();
                      showToastMessage(
                          context, "Please provide the switch point number");
                      return;
                    }
                    final switchPointNumber =
                        switchPointNumberController.text.trim();

                    final spFormatRegex = RegExp(r'^\d{3,6}$');

                    if (!spFormatRegex.hasMatch(switchPointNumber)) {
                      FocusScope.of(context).unfocus();
                      alertPopUp1(
                        context,
                        'Invalid Switch Point Number format. Please enter a valid format (e.g.,SP456,SP001234)',
                        'assets/animation/warn.json',
                      );

                      return;
                    }

                    final rrNumberRegex = RegExp(r'^[a-zA-Z0-9/]+$');

                    if (rrNumberController.text.isNotEmpty) {
                      if (!rrNumberRegex.hasMatch(rrNumberController.text)) {
                        FocusScope.of(context).unfocus();
                        showToastMessage(
                          context,
                          "Invalid RR Number. Provide a proper format",
                        );
                        return;
                      }
                    }

                    if (switchPointIdController.text.isNotEmpty) {
                      // Regex: exactly 13 alphanumeric characters
                      final panelIdRegex = RegExp(r'^[a-zA-Z0-9]{13}$');

                      if (!panelIdRegex
                          .hasMatch(switchPointIdController.text)) {
                        FocusScope.of(context).unfocus();
                        showToastMessage(
                          context,
                          "Invalid Panel ID. It must contain exactly 13 digits.",
                        );
                        return;
                      }
                    }
                    if (spTransformerNoController.text.isNotEmpty) {
                      ref.read(surveyController).updateSurveyFieldValues(
                            context,
                            ref,
                            '$transformerNoPrefixText${spTransformerNoController.text.trim()}',
                            'spTransFormerNo',
                          );
                    }
                    if (switchPointNumber.isNotEmpty) {
                      ref.read(surveyController).updateSurveyFieldValues(
                            context,
                            ref,
                            '$spNoPrefixText$switchPointNumber',
                            'switchPointNumber',
                          );
                    }

                    // All validations passed
                    await Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) =>
                              const CaptureThreeImagesScreen()),
                    );
                  }),
              const SizedBox(
                height: 10,
              ),
            ])))));
  }
}
